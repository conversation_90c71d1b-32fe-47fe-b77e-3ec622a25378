import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction, ExecuteProcess
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution, PythonExpression
from launch.conditions import IfCondition
from launch_ros.actions import Node

from nav2_common.launch import Rewritten<PERSON><PERSON>l


def generate_launch_description():

    pkg_robotcar_fusion = get_package_share_directory('robotcar_laser_fusion')
    pkg_robotcar_nav = get_package_share_directory('robotcar_nav')
    pkg_robotcar_base = get_package_share_directory('robotcar_base')  # {{ AURA-X: Add - 添加robotcar_base包路径. }}

    # 启动参数
    use_sim_time = LaunchConfiguration('use_sim_time')
    start_rviz = LaunchConfiguration('start_rviz')
    use_ekf = LaunchConfiguration('use_ekf')
    map_file = LaunchConfiguration('map_file')
    namespace = LaunchConfiguration('namespace')
    # {{ AURA-X: Add - 添加控制器选择参数. Approval: 寸止(ID:pb_controller_integration). }}
    use_pb_controller = LaunchConfiguration('use_pb_controller')

    # {{ AURA-X: Modify - 支持控制器选择的参数配置. }}
    # 创建Nav2参数配置 - 根据use_pb_controller参数选择配置文件
    nav2_config_file = PathJoinSubstitution([
        pkg_robotcar_nav, 'config',
        PythonExpression([
            "'nav2_fusion_pb.yaml' if '", use_pb_controller, "' == 'true' else 'nav2_fusion.yaml'"
        ])
    ])

    # 根据命名空间是否为空来决定是否使用root_key
    # 注意：这里需要在运行时处理，暂时保留原有配置
    nav2_configured_params = RewrittenYaml(
        source_file=nav2_config_file,
        root_key=namespace,  # 空字符串时RewrittenYaml会忽略root_key
        param_rewrites={'use_sim_time': use_sim_time},
        convert_types=True,
    )

    # {{ AURA-X: Remove - 移除未使用的参数配置. }}

    # === 1. 启动基础硬件系统（独立启动，避免冲突） ===
    # {{ AURA-X: Fix - 独立启动robotcar_base硬件接口，避免重复启动. }}
    # robotcar_base_launch = IncludeLaunchDescription(
    #     PythonLaunchDescriptionSource(
    #         os.path.join(pkg_robotcar_base, 'launch', 'bringup.launch.py')
    #     ),
    #     launch_arguments={
    #         'use_sim_time': use_sim_time,
    #         'namespace': namespace,
    #     }.items()
    # )

    # === 2. 启动双雷达融合系统（跳过硬件接口） ===
    fusion_bringup_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_fusion, 'launch', 'fusion_01_bringup.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
            'skip_hardware': 'true',  # {{ AURA-X: Fix - 跳过硬件接口启动，避免重复启动冲突. }}
        }.items()
    )

    # === 2. 启动EKF传感器融合（可选） ===
    ekf_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(pkg_robotcar_fusion, 'launch', 'fusion_02_ekf.launch.py')
        ),
        condition=IfCondition(use_ekf),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'namespace': namespace,
        }.items()
    )

    # === 3. 启动Cartographer定位系统 ===
    cartographer_config_dir = PathJoinSubstitution([pkg_robotcar_nav, 'config'])
    # {{ AURA-X: Fix - 参考robotcar_nav_05的成功配置. }}
    cartographer_config_basename = 'cartographer_localization.lua'  # {{ AURA-X: Fix - 使用正确的配置文件名. }}
    rviz_config_file = PathJoinSubstitution([pkg_robotcar_nav, 'rviz', 'nav2.rviz'])

    # {{ AURA-X: Fix - 移除TimerAction，直接启动Cartographer（参考robotcar_nav_05）. }}
    cartographer_localization_node = Node(
        package='cartographer_ros',
        executable='cartographer_node',
        name='cartographer_node',
        namespace=namespace,
        output='screen',
        parameters=[
            {'use_sim_time': use_sim_time},
        ],
        arguments=[
            '-configuration_directory', cartographer_config_dir,
            '-configuration_basename', cartographer_config_basename,
            '-load_state_filename', map_file
        ],
        remappings=[
            # {{ AURA-X: Fix - 简化话题重映射，参考robotcar_nav_05. }}
            ('scan', 'merged'),  # 使用融合后的激光雷达数据
            ('imu', 'imu_sensor_broadcaster/imu'),
            ('odom', 'odom'),
        ]
    )

    # === 4. 启动Cartographer占用栅格节点 ===
    # {{ AURA-X: Fix - 移除TimerAction，直接启动（参考robotcar_nav_05）. }}
    occupancy_grid_node = Node(
        package='cartographer_ros',
        executable='cartographer_occupancy_grid_node',
        name='cartographer_occupancy_grid_node',
        namespace=namespace,
        output='screen',
        parameters=[{'use_sim_time': use_sim_time}],
        arguments=['-resolution', '0.03', '-publish_period_sec', '1.0']
    )

    # === 5. 启动Nav2导航系统 ===
    # {{ AURA-X: Fix - 使用二进制安装的Nav2，参考robotcar_nav_05的成功模式. }}
    nav2_navigation_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            '/opt/ros/humble/share/nav2_bringup/launch/navigation_launch.py'
        ),
        launch_arguments={
            'use_sim_time': use_sim_time,
            'params_file': nav2_configured_params,
            'autostart': 'true',
            'namespace': namespace,
        }.items()
    )

    # === 6. 启动RViz可视化 ===
    navigation_rviz_node = TimerAction(
        period=8.0,  # 等待所有节点启动
        actions=[
            ExecuteProcess(
                cmd=['rviz2', '-d', rviz_config_file],
                output='screen',
                condition=IfCondition(start_rviz),
                additional_env={'DISPLAY': ':0'}
            )
        ]
    )
    # === 7. 启动cmd_vel话题重映射节点 ===
    # {{ AURA-X: Modify - 修复cmd_vel重映射的命名空间配置. Approval: 寸止(ID:1735659600). }}
    cmd_vel_relay_node = TimerAction(
        period=1.0,  # 等待Nav2启动
        actions=[
            Node(
                package='topic_tools',
                executable='relay',
                name='cmd_vel_relay',
                output='screen',
                arguments=['cmd_vel', 'diff_drive_controller/cmd_vel_unstamped'],  # {{ AURA-X: Modify - 简化话题重映射. }}
                parameters=[{'use_sim_time': use_sim_time}],  # {{ AURA-X: Fix - 使用直接参数配置. }}
                namespace=namespace,  # {{ AURA-X: Modify - 使用传入的命名空间参数. }}
            )
        ]
    )
    return LaunchDescription([
        # === 启动参数声明 ===
        DeclareLaunchArgument(
            'use_sim_time',
            default_value='False',  # 修复：统一使用Python布尔值格式
            description='Use simulation time if true'
        ),

        DeclareLaunchArgument(
            'start_rviz',
            default_value='True',  # 默认启动RViz
            description='Start RViz for navigation visualization'
        ),

        DeclareLaunchArgument(
            'use_ekf',
            default_value='True',
            description='Use EKF for sensor fusion'
        ),

        DeclareLaunchArgument(
            'map_file',
            default_value='/home/<USER>/test_ws/src/robotcar_nav/maps/0805.pbstream',
            description='Path to the map file for navigation (.yaml file)'
        ),

        DeclareLaunchArgument(
            'namespace',
            default_value='',
            description='ROS2 namespace (optional, default: empty for root namespace)'
        ),

        # {{ AURA-X: Add - 添加控制器选择参数. Approval: 寸止(ID:pb_controller_integration). }}
        DeclareLaunchArgument(
            'use_pb_controller',
            default_value='true',
            description='Use pb_omni_pid_pursuit_controller instead of DWB (true/false)'
        ),

        # === 系统启动 ===
        fusion_bringup_launch,          # {{ AURA-X: Fix - 双雷达融合系统（跳过硬件接口）. }}
        ekf_launch,                     # EKF传感器融合（可选）
        cartographer_localization_node, # Cartographer定位
        occupancy_grid_node,            # Cartographer占用栅格节点
        nav2_navigation_launch,         # {{ AURA-X: Fix - Nav2导航系统（使用二进制安装版本）. }}
        cmd_vel_relay_node,             # cmd_vel话题重映射
        navigation_rviz_node,           # RViz可视化
    ])
